#!/usr/bin/env python3
"""
Test the new sub-agent architecture for relationship validation and query execution
"""

def test_direction_analysis():
    """Test the enhanced direction analysis functionality"""
    print("🔍 Testing relationship direction analysis...")
    
    try:
        from epg_tv_guide import check_relationship_exists
        
        # Test BROADCASTS relationship between Program and Channel in both directions
        print("\n1. Testing BROADCASTS relationship directions:")
        result = check_relationship_exists("BROADCASTS", "Program", "Channel")
        
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   Exists in database: {result.get('exists_in_database', False)}")
        
        direction_analysis = result.get('direction_analysis', {})
        if direction_analysis and 'error' not in direction_analysis:
            print(f"   Direction analysis:")
            for pattern, info in direction_analysis.items():
                if isinstance(info, dict) and 'count' in info:
                    print(f"     {pattern}: {info['count']} relationships, exists: {info['exists']}")
            
            recommended = direction_analysis.get('recommended_direction', 'None')
            recommended_count = direction_analysis.get('recommended_count', 0)
            print(f"   Recommended direction: {recommended} (count: {recommended_count})")
            
            return recommended_count > 0
        else:
            print(f"   Direction analysis error: {direction_analysis.get('error', 'Unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing direction analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_subagent_imports():
    """Test that the new sub-agents can be imported"""
    print("\n🤖 Testing sub-agent imports...")
    
    try:
        from epg_tv_guide import (
            super_sport_agent, 
            relationship_validator_agent, 
            query_executor_agent
        )
        
        print("✅ Successfully imported all agents")
        print(f"   Root agent: {super_sport_agent.name}")
        print(f"   Validator agent: {relationship_validator_agent.name}")
        print(f"   Executor agent: {query_executor_agent.name}")
        
        # Check sub-agents in root agent
        if hasattr(super_sport_agent, 'sub_agents') and super_sport_agent.sub_agents:
            sub_agent_names = [agent.name for agent in super_sport_agent.sub_agents]
            print(f"   Root agent sub-agents: {sub_agent_names}")
            
            expected_subagents = ['relationship_validator_agent', 'query_executor_agent']
            missing_subagents = [name for name in expected_subagents if name not in sub_agent_names]
            
            if not missing_subagents:
                print("✅ All expected sub-agents are configured")
                return True
            else:
                print(f"❌ Missing sub-agents: {missing_subagents}")
                return False
        else:
            print("❌ Root agent has no sub-agents configured")
            return False
            
    except Exception as e:
        print(f"❌ Error importing sub-agents: {e}")
        return False

def test_formula_one_direction_fix():
    """Test that Formula One queries now use the correct relationship direction"""
    print("\n🏎️ Testing Formula One direction fix...")
    
    try:
        from epg_tv_guide import check_relationship_exists
        
        # First, determine the correct direction for BROADCASTS between Program and Channel
        print("   Checking BROADCASTS relationship directions...")
        result = check_relationship_exists("BROADCASTS", "Program", "Channel")
        
        direction_analysis = result.get('direction_analysis', {})
        if direction_analysis and 'recommended_direction' in direction_analysis:
            recommended_direction = direction_analysis['recommended_direction']
            recommended_count = direction_analysis['recommended_count']
            
            print(f"   Recommended direction: {recommended_direction}")
            print(f"   Relationship count: {recommended_count}")
            
            # Check if it's the correct direction (Program -> Channel)
            if "(Program)-[:BROADCASTS]->(Channel)" in recommended_direction:
                print("✅ Correct direction: (Program)-[:BROADCASTS]->(Channel)")
                return True
            elif "(Channel)-[:BROADCASTS]->(Program)" in recommended_direction:
                print("⚠️  Direction is: (Channel)-[:BROADCASTS]->(Program)")
                print("   This means the previous query direction was incorrect")
                return True
            else:
                print(f"❌ Unexpected direction format: {recommended_direction}")
                return False
        else:
            print("❌ Could not determine recommended direction")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Formula One direction fix: {e}")
        return False

def test_subagent_tools():
    """Test that sub-agents have the correct tools"""
    print("\n🛠️ Testing sub-agent tools...")
    
    try:
        from epg_tv_guide import relationship_validator_agent, query_executor_agent
        
        # Test relationship validator agent tools
        if hasattr(relationship_validator_agent, 'tools') and relationship_validator_agent.tools:
            validator_tools = [tool.__name__ for tool in relationship_validator_agent.tools if hasattr(tool, '__name__')]
            print(f"   Validator agent tools: {validator_tools}")
            
            expected_validator_tools = ['check_relationship_exists', 'inspect_schema']
            validator_ok = all(tool in validator_tools for tool in expected_validator_tools)
            
            if validator_ok:
                print("✅ Relationship validator has correct tools")
            else:
                print("❌ Relationship validator missing tools")
        else:
            print("❌ Relationship validator has no tools")
            validator_ok = False
        
        # Test query executor agent tools
        if hasattr(query_executor_agent, 'tools') and query_executor_agent.tools:
            executor_tools = [tool.__name__ for tool in query_executor_agent.tools if hasattr(tool, '__name__')]
            print(f"   Executor agent tools: {executor_tools}")
            
            expected_executor_tools = ['query_graph']
            executor_ok = all(tool in executor_tools for tool in expected_executor_tools)
            
            if executor_ok:
                print("✅ Query executor has correct tools")
            else:
                print("❌ Query executor missing tools")
        else:
            print("❌ Query executor has no tools")
            executor_ok = False
            
        return validator_ok and executor_ok
        
    except Exception as e:
        print(f"❌ Error testing sub-agent tools: {e}")
        return False

def demonstrate_correct_workflow():
    """Demonstrate the correct workflow for Formula One queries"""
    print("\n🔄 Demonstrating correct workflow...")
    
    print("CORRECT WORKFLOW FOR FORMULA ONE QUERIES:")
    print("1. ✅ User asks: 'When is Formula One airing?'")
    print("2. ✅ Root agent coordinates sub-agents")
    print("3. ✅ Relationship validator checks BROADCASTS direction")
    print("4. ✅ Validator finds: (Program)-[:BROADCASTS]->(Channel) has data")
    print("5. ✅ Query executor uses correct direction")
    print("6. ✅ Generated query: MATCH (p:Program)-[:BROADCASTS]->(c:Channel) WHERE...")
    print("7. ✅ Query returns actual Formula One schedule data")
    
    print("\nPREVIOUS ISSUE (now fixed):")
    print("❌ Query used: (Channel)-[:BROADCASTS]->(Program)")
    print("❌ This direction had no data, so empty results")
    print("❌ User got: 'I don't know the answer'")
    
    print("\nNEW SOLUTION:")
    print("✅ Direction validation ensures correct pattern")
    print("✅ Query uses: (Program)-[:BROADCASTS]->(Channel)")
    print("✅ This direction has actual data")
    print("✅ User gets: Actual Formula One schedule information")

def main():
    """Run all tests for the sub-agent architecture"""
    print("Testing Sub-Agent Architecture for Relationship Validation")
    print("=" * 70)
    
    # Test 1: Direction analysis
    direction_ok = test_direction_analysis()
    
    # Test 2: Sub-agent imports
    import_ok = test_subagent_imports()
    
    # Test 3: Formula One direction fix
    formula_one_ok = test_formula_one_direction_fix()
    
    # Test 4: Sub-agent tools
    tools_ok = test_subagent_tools()
    
    # Test 5: Workflow demonstration
    demonstrate_correct_workflow()
    
    print("\n" + "=" * 70)
    print("TEST RESULTS:")
    print(f"Direction Analysis: {'✅ PASS' if direction_ok else '❌ FAIL'}")
    print(f"Sub-Agent Imports: {'✅ PASS' if import_ok else '❌ FAIL'}")
    print(f"Formula One Fix: {'✅ PASS' if formula_one_ok else '❌ FAIL'}")
    print(f"Sub-Agent Tools: {'✅ PASS' if tools_ok else '❌ FAIL'}")
    
    if all([direction_ok, import_ok, formula_one_ok, tools_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("The sub-agent architecture is working correctly.")
        print("\nKey Features:")
        print("✅ Relationship direction validation")
        print("✅ Specialized sub-agents for validation and execution")
        print("✅ Correct Formula One query direction")
        print("✅ Prevents empty results due to wrong directions")
        print("✅ Orchestrated workflow with proper coordination")
        
        print("\nFormula One queries should now:")
        print("✅ Use the correct relationship direction")
        print("✅ Return actual schedule data instead of empty results")
        print("✅ Provide accurate 'When is Formula One airing?' answers")
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("Check the failed tests above for issues to resolve.")

if __name__ == "__main__":
    main()
