# Formula One Query Solution - Enhanced EPG Chat Bot

## Problem Statement

The EPG Chat Bot was generating incorrect Cypher queries for Formula One program scheduling questions. Specifically:

**Failing Query:**
```cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel) 
WHERE LOWER(p.id) CONTAINS LOWER('Formula One') 
RETURN p.id, c.id
```

**Working Query:**
```cypher
MATCH (p:Program)-[:BROADCASTS]->(c:Channel) 
WHERE LOWER(p.id) CONTAINS LOWER('Formula One') 
RETURN p.id, c.id
```

The issue was that the agent was using the wrong relationship name (`AIRS_ON` instead of `BROADCASTS`).

## Solution Implemented

### 1. Schema Inspection Sub-Agent

Created a new `inspect_schema()` function that:
- Connects to the Neo4j database
- Retrieves all relationship types and node labels
- Specifically identifies Program-Channel relationships
- Returns schema information for query validation

**Location:** `epg_tv_guide/agents/agent.py` (lines 25-60)

### 2. Query Validation and Correction

Implemented `validate_and_correct_query()` function that:
- Inspects the database schema before query generation
- Detects program/channel-related queries using keywords
- Provides context about correct relationships to the LLM
- Enhances questions with schema information

**Location:** `epg_tv_guide/agents/agent.py` (lines 62-129)

### 3. Enhanced Cypher Generation

Updated the prompt template in `KnowledgeGraphService` to:
- Explicitly specify correct relationship mappings
- Provide clear guidance on Program-Channel relationships
- Include common query patterns
- Emphasize the use of `BROADCASTS` relationship

**Location:** `epg_tv_guide/services/knowledge_graph_service.py` (lines 127-152)

### 4. Updated Agent Configuration

Modified the main agent to:
- Include both `query_graph` and `inspect_schema` tools
- Updated instructions to mention schema validation capabilities
- Enhanced workflow description

**Location:** `epg_tv_guide/agents/agent.py` (lines 131-185)

## Key Features

### Automatic Schema Detection
```python
def inspect_schema() -> Dict[str, Any]:
    """Inspect Neo4j database schema to understand available relationships"""
    # Returns relationship types, node labels, and Program-Channel relationships
```

### Smart Query Enhancement
```python
def validate_and_correct_query(question: str) -> Dict[str, Any]:
    """Validate and correct common relationship issues before querying"""
    # Detects program/channel queries and adds schema context
```

### Enhanced Prompt Template
```
IMPORTANT RELATIONSHIP MAPPINGS:
- Program to Channel: Use (Channel)-[:BROADCASTS]->(Program) 
- Program to Team: Use (Program)-[:INVOLVES]->(Team)
- Team to Tournament: Use (Team)-[:COMPETES_IN]->(Tournament)
```

## Usage

### For Formula One Queries
```python
from epg_tv_guide import query_graph

# This will now use the correct BROADCASTS relationship
result = query_graph("When is the Formula One program airing?")
print(result['cypher_query'])  # Should use BROADCASTS
```

### For Schema Inspection
```python
from epg_tv_guide import inspect_schema

# Get database schema information
schema_info = inspect_schema()
print(schema_info['program_channel_relationships'])
```

## Files Modified

1. **`epg_tv_guide/agents/agent.py`**
   - Added `inspect_schema()` function
   - Added `validate_and_correct_query()` function
   - Updated `query_graph()` to use validation
   - Added `inspect_schema` to agent tools
   - Updated agent instructions

2. **`epg_tv_guide/services/knowledge_graph_service.py`**
   - Enhanced Cypher generation prompt template
   - Added explicit relationship mappings
   - Included common query patterns

3. **`epg_tv_guide/agents/__init__.py`**
   - Added `inspect_schema` to exports

4. **`epg_tv_guide/__init__.py`**
   - Added `inspect_schema` to package exports

## Benefits

1. **Automatic Relationship Correction**: Detects and corrects wrong relationship usage
2. **Schema Awareness**: Queries are generated based on actual database schema
3. **Improved Accuracy**: Reduces query failures due to incorrect relationships
4. **Debugging Capability**: Schema inspection helps troubleshoot database issues
5. **Future-Proof**: Adapts to schema changes automatically

## Testing

To test the solution:

1. **Install Dependencies:**
   ```bash
   pip install neo4j langchain langchain-community
   ```

2. **Test Formula One Query:**
   ```python
   from epg_tv_guide import query_graph
   result = query_graph("When is the Formula One program airing?")
   print(f"Generated Query: {result['cypher_query']}")
   print(f"Answer: {result['answer']}")
   ```

3. **Verify Schema Detection:**
   ```python
   from epg_tv_guide import inspect_schema
   schema = inspect_schema()
   print(f"Available relationships: {schema['relationships']}")
   ```

## Expected Results

With this enhancement, the Formula One query should now generate:
```cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program) 
WHERE LOWER(p.name) CONTAINS LOWER('Formula One') 
RETURN c.name, p.name, p.start_time
```

Instead of the incorrect `AIRS_ON` relationship, ensuring successful query execution and accurate results.
