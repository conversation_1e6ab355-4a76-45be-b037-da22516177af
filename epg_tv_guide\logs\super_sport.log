2025-05-21 15:45:51,023 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:53:33,713 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:53:35,708 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next football match?
2025-05-21 15:53:38,891 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: In order to use this chain, you must acknowledge that it can make dangerous requests by setting `allow_dangerous_requests` to `True`.You must narrowly scope the permissions of the database connection to only include necessary permissions. Failure to do so may result in data corruption or loss or reading sensitive data if such data is present in the database.Only use this chain if you understand the risks and have taken the necessary precautions. See https://python.langchain.com/docs/security for more information.
2025-05-21 15:55:56,540 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:55:58,334 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are channels shows sports programs?
2025-05-21 15:56:03,789 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)<-[:BROADCAST_ON]-(e:Event)-[:BELONGS_TO]->(s:Sport)
RETURN c, e, s

2025-05-21 15:56:37,848 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next live matches?
2025-05-21 15:56:40,016 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)
WHERE e.isLive = true
RETURN e.title, e.start
ORDER BY e.start
LIMIT 1

2025-05-21 15:57:44,260 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Show all the completed programs
2025-05-21 15:57:47,049 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)
WHERE e.isLive = false
RETURN e

2025-05-21 16:00:59,256 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Any Rugby matches today?
2025-05-21 16:01:01,329 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE e.sport = "Rugby" AND e.start CONTAINS date()
RETURN e

2025-05-21 16:01:31,025 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Are the any Rugby matches?
2025-05-21 16:01:33,557 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Rugby"
RETURN e

2025-05-21 20:53:46,187 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 20:53:49,014 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When are the next upcoming football matches?
2025-05-21 20:53:56,466 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Football" AND e.isLive = true
RETURN e.start, e.title
ORDER BY e.start

2025-05-21 20:55:12,279 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are the completed football matches?
2025-05-21 20:55:14,919 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Football" AND e.isLive = false
RETURN e

2025-05-21 20:57:09,792 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of super sport variety today
2025-05-21 20:57:11,731 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "SuperSport Variety" AND e.start CONTAINS date()
RETURN e

2025-05-21 20:58:22,900 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of SuperSport Blitz today
2025-05-21 20:58:24,786 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BROADCAST_ON]->(c:Channel)
WHERE c.name = "SuperSport Blitz" AND e.start CONTAINS date()
RETURN e

2025-05-21 20:59:13,893 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the upcoming programs of SuperSport Blitz
2025-05-21 20:59:15,687 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel {name: "SuperSport Blitz"})<-[:BROADCAST_ON]-(e:Event)
WHERE e.start > datetime()
RETURN e

2025-05-21 20:59:40,469 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of SuperSport Blitz
2025-05-21 20:59:42,363 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BROADCAST_ON]->(c:Channel)
WHERE c.name = "SuperSport Blitz"
RETURN e

2025-05-21 21:00:29,012 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the program time of "Sports Bulletin Around The Clock"?
2025-05-21 21:00:31,351 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event {title: "Sports Bulletin Around The Clock"})
RETURN e.start AS StartTime, e.end AS EndTime

2025-05-21 21:04:22,931 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 09:56:14,049 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 09:59:06,926 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:11:10,455 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:21:30,370 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:25:17,891 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:41:08,150 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:41:09,882 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are the upcoming football matches scheduled next?
2025-05-22 10:41:16,637 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `groq/llama-3.1-8b-instant` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 11:06:22,381 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 12:41:03,583 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 13:27:37,012 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 00:26:51,577 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 00:26:53,537 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the mumbai indians match
2025-05-29 00:26:59,096 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (t:Team {name: "mumbai indians"})-[:COMPETES_IN]->(p:Program)
RETURN p

2025-05-29 10:42:47,347 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 10:42:50,518 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next Mumbai Indians match?
2025-05-29 10:42:55,178 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (team:Team {name: "Mumbai Indians"})-[:COMPETES_IN]->(tournament:Tournament)<-[:COMPETES_IN]-(otherTeam:Team),
      (team)-[:COMPETES_AGAINST]->(otherTeam),
      (program:Program)-[:INVOLVES]->(team)
WHERE team.name = "Mumbai Indians"
RETURN program.start_time AS NextMatchTime
ORDER BY NextMatchTime
LIMIT 1

2025-05-29 10:54:33,708 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 11:02:21,172 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 11:02:23,543 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next Mumbai Indians match?
2025-05-29 11:02:29,819 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (team:Team {name: "Mumbai Indians"})-[:COMPETES_IN]->(tournament:Tournament)<-[:COMPETES_IN]-(otherTeam:Team)
WITH team, tournament, otherTeam
MATCH (channel:Channel)-[broadcasts:BROADCASTS]->(program:Program)-[:INVOLVES]->(team),
      (program)-[:INVOLVES]->(otherTeam)
RETURN team.name AS Team1, otherTeam.name AS Team2, tournament.name AS Tournament, broadcasts.start_time AS StartTime
ORDER BY broadcasts.start_time
LIMIT 1

2025-05-29 11:02:47,839 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Any CSK match?
2025-05-29 11:02:49,789 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (t:Team {name: "CSK"})-[:COMPETES_IN]->(tournament)
RETURN t, tournament

2025-05-29 11:03:20,052 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Chennai Super Kings
2025-05-29 11:03:22,437 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (t:Team {name: "Chennai Super Kings"})
RETURN t

2025-05-29 11:35:37,655 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Show me all the netball programs
2025-05-29 11:35:41,792 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Netball"
RETURN p

2025-05-29 11:37:04,056 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next CSK match
2025-05-29 11:37:06,819 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (team:Team {name: "CSK"})-[:COMPETES_IN]->(tournament:Tournament)
WITH team, tournament
MATCH (team)-[:COMPETES_AGAINST]->(otherTeam:Team)
MATCH (program:Program)-[:INVOLVES]->(team)
RETURN program.start_time AS NextMatchTime
ORDER BY NextMatchTime
LIMIT 1

2025-05-29 11:43:26,881 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 11:47:52,144 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 11:47:53,882 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next MI match?
2025-05-29 11:47:58,714 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (team:Team)-[:INVOLVES]->(program:Program)
WHERE (toLower(team.name) CONTAINS "mi" OR toLower(team.name) CONTAINS "mumbai indians") AND program.start_time > datetime()
RETURN program.start_time AS NextMatchTime, program.name AS MatchName
ORDER BY program.start_time ASC
LIMIT 1

2025-05-29 12:12:13,882 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 12:12:17,484 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next MI match?
2025-05-29 12:12:24,297 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (program:Program)-[:INVOLVES]->(team:Team)
WHERE (toLower(team.name) CONTAINS "mi" OR toLower(team.name) CONTAINS "mumbai indians")
  AND program.start_time > datetime()
RETURN program.start_time AS NextMatchTime, program.name AS MatchName
ORDER BY program.start_time ASC
LIMIT 1

2025-05-29 12:16:02,573 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 12:16:06,125 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next MI match?
2025-05-29 12:16:12,614 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (program:Program)-[:INVOLVES]->(team:Team)
WHERE (toLower(team.name) CONTAINS "mi" OR toLower(team.name) CONTAINS "mumbai indians")
  AND program.start_time > datetime()
RETURN program.start_time AS NextMatchTime, program.name AS MatchName
ORDER BY program.start_time ASC
LIMIT 1

2025-05-29 15:22:35,810 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 15:22:38,182 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next Mumbai Indians match?
2025-05-29 15:22:44,567 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (t:Team {name: "Mumbai Indians"})-[:COMPETES_IN]->(tournament)
MATCH (t)-[:COMPETES_AGAINST]->(opponent)
MATCH (p:Program)-[:INVOLVES]->(t)
RETURN p.start_time
ORDER BY p.start_time
LIMIT 1

2025-05-29 15:24:05,472 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What school sports are playing tonight?
2025-05-29 15:24:07,688 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input 'There': expected 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'FOREACH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"There is no information about school sports. I can not create a Cypher statement."
 ^}
2025-05-29 15:25:15,182 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What live sports are available?
2025-05-29 15:25:17,438 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program)-[:BELONGS_TO]->(s:Sport)
WHERE p.is_live = true
RETURN DISTINCT s.name

2025-05-29 15:26:06,988 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What variety shows are on this week?
2025-05-29 15:26:09,329 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:HAS_GENRE]->(g:Genre)
WHERE g.name = "Variety"
RETURN p.name

2025-05-29 15:27:34,125 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Are there any finals or championships today?
2025-05-29 15:27:36,242 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (t:Tournament)
WHERE t.type IN ['Final', 'Championship']
RETURN t

2025-05-29 16:21:03,374 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 16:26:13,438 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next Rugby match?
2025-05-29 16:26:18,377 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: {code: Neo.ClientError.Statement.SyntaxError} {message: Unexpected end of input: expected CYPHER, EXPLAIN, PROFILE or Query (line 1, column 1 (offset: 0))
"cypher"
 ^}
2025-05-29 17:32:09,976 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 17:32:12,419 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next Cricket match?
2025-05-29 17:32:18,639 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (s:Sport {name: "Cricket"})<-[:BELONGS_TO]-(p:Program)
RETURN p.start_time
ORDER BY p.start_time
LIMIT 1

2025-05-30 10:13:22,533 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-30 10:13:24,330 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What live sports are available?
2025-05-30 10:13:31,074 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)-[b:BROADCASTS]->(p:Program)
WHERE p.is_live = true AND p.sport IS NOT NULL
RETURN DISTINCT p.sport

2025-05-30 10:13:53,464 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What variety shows are on this week?
2025-05-30 10:13:56,093 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (g:Genre)<-[:HAS_GENRE]-(p:Program)
WHERE g.name = "Variety"
RETURN p

2025-05-30 10:14:20,494 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What's on SuperSport School HD today?
2025-05-30 10:14:22,699 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel {name: "SuperSport School HD"})-[b:BROADCASTS]->(p:Program)
WHERE b.start_time >= date() AND b.start_time < date() + duration({days: 1})
RETURN p

2025-05-30 10:15:17,586 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is GamePlay Show airing?
2025-05-30 10:15:19,589 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)-[b:BROADCASTS]->(p:Program)
WHERE p.name = "GamePlay Show"
RETURN c.name, b.start_time, b.end_time

2025-06-16 14:17:47,984 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 14:17:50,089 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is GamePlay Show airing?
2025-06-16 14:17:59,446 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program {id: "GamePlay Show"})-[:STARTS_AT]->(t:Timeslot)
RETURN t

2025-06-16 14:19:37,529 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is SS Football Show airing?
2025-06-16 14:19:39,440 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)
WHERE p.id = "SS Football Show"
RETURN p.STARTS_AT, p.ENDS_AT

2025-06-16 14:25:52,198 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is Supersport Blitz airing?
2025-06-16 14:25:55,314 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)
WHERE p.id = "Supersport Blitz"
RETURN p.id, c.id

2025-06-16 14:26:22,429 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: I need time of Supersport Blitz airing
2025-06-16 14:26:24,493 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (d:Document)
WHERE d.channel = "Supersport Blitz"
RETURN d.start_time, d.end_time

2025-06-16 14:26:52,880 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is Supersport Blitz program airing?
2025-06-16 14:26:55,065 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program {id: "Supersport Blitz"})-[:STARTS_AT]->(t:Timeslot)
RETURN t.id

2025-06-16 14:32:23,255 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 14:32:25,187 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the MI match?
2025-06-16 14:32:30,203 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (m:Match) WHERE m.id = "MI" RETURN m

2025-06-16 14:33:57,822 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is South Africa match?
2025-06-16 14:33:59,840 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (m:Match)-[:VERSUS]->(c:Country)
WHERE c.id = "South Africa"
RETURN m

2025-06-16 14:38:00,510 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What's the lineup for SuperSport OTT 7?
2025-06-16 14:38:02,850 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel {id: "SuperSport OTT 7"})-[:BROADCASTS]->(p:Program)
RETURN p

2025-06-16 18:36:27,667 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 18:36:29,675 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is "F1" Show airing?
2025-06-16 18:36:35,021 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)
WHERE LOWER(p.id) = LOWER('F1')
RETURN p.id, c.id

2025-06-16 19:06:19,989 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:11:40,927 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:21:11,916 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:21:14,471 - epg_tv_guide.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the Formula One race?
2025-06-16 19:21:21,857 - epg_tv_guide.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (d:Document)
WHERE LOWER(d.text) CONTAINS LOWER('Formula One race')
RETURN d.start_time, d.end_time

2025-06-16 19:21:50,331 - epg_tv_guide.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the Formula One program airing?
2025-06-16 19:21:52,715 - epg_tv_guide.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)
WHERE LOWER(p.id) CONTAINS LOWER('Formula One')
RETURN p.id, c.id

2025-06-16 19:32:33,464 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:32:33,464 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:33,464 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:33,482 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:33,486 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:33,488 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:39,081 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:32:39,081 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:39,081 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:39,098 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:39,100 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:32:39,102 - epg_tv_guide.services.knowledge_graph_service - ERROR - Error connecting to Neo4j: Could not import neo4j python package. Please install it with `pip install neo4j`.
2025-06-16 19:34:28,813 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:34:28,816 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:40:24,957 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:49:42,368 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 19:49:47,398 - epg_tv_guide.agents.agent - INFO - Most common Program-Channel relationship: AIRS_ON
2025-06-16 19:49:47,399 - epg_tv_guide.services.knowledge_graph_service - INFO - Querying knowledge graph: 
                When is the Formula One program airing?

                IMPORTANT: Use the relationship 'AIRS_ON' to connect Program and Channel nodes.
                Available relationships: MENTIONS, LOCATED_IN, VERSUS, AIRS_ON, FEATURES, SPONSORED_BY, COMPETES_IN, TAKES_PLACE_AT, BROADCASTS, PART_OF_SEASON, HAS_GENRE, MEMBER_OF, AVAILABLE_ON, COMMENTARY_BY, ABBREVIATION_OF, ALIAS_OF, PARTICIPATES_IN, INCLUDES, IN_ROUND, CATEGORIZED_AS, HOSTED_BY, BASED_IN, BELONGS_TO, REPRESENTS, ALSO_KNOWN_AS, PRESENTED_BY, HOME_VENUE, HIGHLIGHTS_OF, DURING, PRECEDES, EPISODE_OF, FOLLOWS, LOCATED_AT, SHORT_FORM_OF, STARTS_AT, ENDS_AT, PLAYS_FOR, CONTAINS, TAGGED_WITH
                
2025-06-16 19:49:50,084 - epg_tv_guide.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)
WHERE LOWER(p.id) CONTAINS LOWER('Formula One')
RETURN p, c

2025-06-16 20:04:59,085 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 20:06:55,139 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 20:07:00,847 - epg_tv_guide.agents.agent - INFO - Using most common Program-Channel relationship: AIRS_ON
2025-06-16 20:07:00,849 - epg_tv_guide.services.knowledge_graph_service - INFO - Querying knowledge graph: 
                When is the Formula One program airing?

                INSTRUCTION: Use the relationship 'AIRS_ON' to connect Program and Channel nodes.
                The correct pattern is: (Channel)-[:AIRS_ON]->(Program)
                
2025-06-16 20:07:03,716 - epg_tv_guide.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program)
WHERE LOWER(p.id) CONTAINS LOWER('Formula One')
RETURN c, p

2025-06-16 20:21:59,830 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 20:22:07,635 - epg_tv_guide.agents.agent - INFO - Checking if relationship 'BROADCASTS' exists
2025-06-16 20:22:07,966 - epg_tv_guide.agents.agent - INFO - Checking if relationship 'BROADCASTS' exists
2025-06-16 20:22:14,010 - epg_tv_guide.agents.agent - INFO - Using most common Program-Channel relationship: AIRS_ON
2025-06-16 20:22:14,011 - epg_tv_guide.services.knowledge_graph_service - INFO - Querying knowledge graph: 
                When is Formula One airing on channels?

                INSTRUCTION: Use the relationship 'AIRS_ON' to connect Program and Channel nodes.
                The correct pattern is: (Channel)-[:AIRS_ON]->(Program)
                
2025-06-16 20:22:16,584 - epg_tv_guide.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program)
WHERE LOWER(p.id) CONTAINS LOWER('Formula One')
RETURN c, p

2025-06-16 20:30:29,567 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-06-16 20:30:35,924 - epg_tv_guide.agents.agent - INFO - Checking if relationship 'BROADCASTS' exists
2025-06-16 20:30:36,238 - epg_tv_guide.agents.agent - INFO - Program-Channel validation: (Program)-[:BROADCASTS]->(Channel) (count: 8)
2025-06-16 20:53:31,267 - epg_tv_guide.services.knowledge_graph_service - INFO - Initialized knowledge graph service
