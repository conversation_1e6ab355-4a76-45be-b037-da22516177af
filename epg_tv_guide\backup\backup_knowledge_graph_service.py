"""
Knowledge Graph service for Neo4j operations
"""

import os
import logging
import json
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

import google.generativeai as genai
from langchain_community.graphs import Neo4jGraph
from langchain.chains import GraphCypher<PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeAI
from langchain.prompts import PromptTemplate

from ..utils.logger import get_logger
from ..config.settings import (
    GOOGLE_API_KEY,
    MODEL_GEMINI_2_0_FLASH,
    NEO4J_URI,
    NEO4J_USERNAME,
    NEO4J_PASSWORD,
    NEO4J_DATABASE
)

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI with API key
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY not set. LLM functionality will not work properly.")

def serialize_neo4j_objects(obj: Any) -> Any:
    """
    Recursively convert Neo4j objects to serializable formats
    
    Args:
        obj: Object that may contain Neo4j datetime objects
        
    Returns:
        Serializable version of the object
    """
    # Handle Neo4j DateTime objects
    if hasattr(obj, '__class__') and 'neo4j.time' in str(obj.__class__):
        if hasattr(obj, 'to_native'):
            # Convert Neo4j DateTime to Python datetime, then to ISO string
            return obj.to_native().isoformat()
        else:
            # Fallback to string representation
            return str(obj)
    
    # Handle dictionaries
    elif isinstance(obj, dict):
        return {key: serialize_neo4j_objects(value) for key, value in obj.items()}
    
    # Handle lists
    elif isinstance(obj, list):
        return [serialize_neo4j_objects(item) for item in obj]
    
    # Handle tuples
    elif isinstance(obj, tuple):
        return tuple(serialize_neo4j_objects(item) for item in obj)
    
    # Return as-is for other types
    else:
        return obj

# Custom Cypher generation prompt that handles abbreviations and aliases
CYPHER_GENERATION_TEMPLATE = """
You are an expert at converting natural language questions to Cypher queries for a sports database.

Database Schema:
{schema}

IMPORTANT DATABASE RELATIONSHIP DIRECTIONS:
- (Program)-[:INVOLVES]->(Team) - Programs involve teams
- (Team)-[:COMPETES_IN]->(Tournament) - Teams compete in tournaments  
- (Team)-[:COMPETES_AGAINST]->(Team) - Teams compete against other teams

IMPORTANT INSTRUCTIONS FOR TEAM NAME MATCHING:
1. Teams may be referred to by abbreviations (e.g., "CSK", "MI", "RCB") or full names (e.g., "Chennai Super Kings", "Mumbai Indians", "Royal Challengers Bangalore")
2. ALWAYS use flexible matching patterns when looking for teams:
   - Use CONTAINS or regex matching for partial team names
   - Consider common abbreviations and their full forms
   - Use case-insensitive matching where possible

COMMON TEAM ABBREVIATIONS TO FULL NAMES:
- CSK = Chennai Super Kings
- MI = Mumbai Indians  
- RCB = Royal Challengers Bangalore
- DC = Delhi Capitals
- KKR = Kolkata Knight Riders
- PBKS = Punjab Kings
- RR = Rajasthan Royals
- SRH = Sunrisers Hyderabad
- GT = Gujarat Titans
- LSG = Lucknow Super Giants

QUERY GENERATION RULES:
1. When a team abbreviation is mentioned, search for both the abbreviation AND the full name
2. Use pattern matching like: WHERE (toLower(team.name) CONTAINS "csk" OR toLower(team.name) CONTAINS "chennai super kings")
3. Use case-insensitive matching: WHERE toLower(team.name) CONTAINS toLower("csk") OR toLower(team.name) CONTAINS toLower("chennai")
4. For time-based queries, always order by time and use LIMIT appropriately
5. When looking for "next" events, filter for future dates: WHERE program.start_time > datetime()
6. ALWAYS respect relationship directions as specified above

CORRECT QUERY PATTERNS FOR COMMON QUESTIONS:

For "When is the next [TEAM] match?":
```cypher
MATCH (program:Program)-[:INVOLVES]->(team:Team)
WHERE (toLower(team.name) CONTAINS "[team_abbrev]" OR toLower(team.name) CONTAINS "[team_full_name]") 
  AND program.start_time > datetime()
RETURN program.start_time AS NextMatchTime, program.name AS MatchName
ORDER BY program.start_time ASC 
LIMIT 1
```

For "[TEAM] upcoming matches":
```cypher
MATCH (program:Program)-[:INVOLVES]->(team:Team)
WHERE (toLower(team.name) CONTAINS "[team_abbrev]" OR toLower(team.name) CONTAINS "[team_full_name]") 
  AND program.start_time > datetime()
RETURN program.start_time, program.name, team.name
ORDER BY program.start_time ASC
LIMIT 5
```

For "[TEAM1] vs [TEAM2]" matches:
```cypher
MATCH (program:Program)-[:INVOLVES]->(team1:Team), 
      (program:Program)-[:INVOLVES]->(team2:Team)
WHERE (toLower(team1.name) CONTAINS "[team1_abbrev]" OR toLower(team1.name) CONTAINS "[team1_full_name]")
  AND (toLower(team2.name) CONTAINS "[team2_abbrev]" OR toLower(team2.name) CONTAINS "[team2_full_name]")
  AND team1 <> team2
RETURN program.start_time, program.name, team1.name, team2.name
ORDER BY program.start_time DESC
LIMIT 5
```

Example patterns for flexible team matching:
- For "CSK": WHERE (toLower(team.name) CONTAINS "chennai super kings")
- For "MI": WHERE (toLower(team.name) CONTAINS "mumbai indians")

Generate a Cypher query to answer this question: {question}

Remember to use the CORRECT relationship direction: (Program)-[:INVOLVES]->(Team)

Only return the Cypher query, nothing else.
"""

CYPHER_QA_TEMPLATE = """
You are an assistant that provides answers based on Cypher query results from a sports database.

Context from database: {context}

Question: {question}

Based on the context provided, give a helpful and accurate answer. If the context contains datetime information, format it in a user-friendly way.

Answer:
"""

class KnowledgeGraphService:
    """Service for Neo4j knowledge graph operations using LangChain"""
    
    def __init__(self):
        """Initialize the knowledge graph service"""
        self._graph: Optional[Neo4jGraph] = None
        self._qa_chain: Optional[GraphCypherQAChain] = None
        # Default team abbreviations - can be extended
        self.team_abbreviations = {
            "CSK": "Chennai Super Kings",
            "MI": "Mumbai Indians",
            "RCB": "Royal Challengers Bangalore",
            "DC": "Delhi Capitals",
            "KKR": "Kolkata Knight Riders",
            "PBKS": "Punjab Kings",
            "RR": "Rajasthan Royals",
            "SRH": "Sunrisers Hyderabad",
            "GT": "Gujarat Titans",
            "LSG": "Lucknow Super Giants"
        }
        logger.info("Initialized knowledge graph service")
    
    def add_team_abbreviation(self, abbreviation: str, full_name: str):
        """
        Add a new team abbreviation mapping
        
        Args:
            abbreviation: The abbreviation (e.g., "CSK")
            full_name: The full team name (e.g., "Chennai Super Kings")
        """
        self.team_abbreviations[abbreviation.upper()] = full_name
        # Clear the cached QA chain so it gets recreated with updated mappings
        self._qa_chain = None
        logger.info(f"Added team abbreviation: {abbreviation} -> {full_name}")
    
    def generate_team_match_condition(self, team_input: str) -> str:
        """
        Generate a flexible Cypher WHERE condition for team matching
        
        Args:
            team_input: Team name or abbreviation from user input
            
        Returns:
            Cypher WHERE condition string for flexible team matching
        """
        team_upper = team_input.upper()
        
        # Check if it's a known abbreviation
        if team_upper in self.team_abbreviations:
            full_name = self.team_abbreviations[team_upper]
            return f'(toLower(team.name) CONTAINS "{team_input.lower()}" OR toLower(team.name) CONTAINS "{full_name.lower()}")'
        else:
            # Check if the input might be a full name that has an abbreviation
            for abbrev, full_name in self.team_abbreviations.items():
                if team_input.lower() in full_name.lower():
                    return f'(toLower(team.name) CONTAINS "{abbrev.lower()}" OR toLower(team.name) CONTAINS "{full_name.lower()}")'
            
            # Default: just search for the input term
            return f'toLower(team.name) CONTAINS "{team_input.lower()}"'
    
    def get_neo4j_graph(self) -> Optional[Neo4jGraph]:
        """
        Get the Neo4j graph connection
        
        Returns:
            Neo4jGraph instance or None if not available
        """
        try:
            # If we already have a loaded graph connection
            if self._graph:
                return self._graph
            
            # Create new Neo4j graph connection
            self._graph = Neo4jGraph(
                url=NEO4J_URI,
                username=NEO4J_USERNAME, 
                password=NEO4J_PASSWORD,
                database=NEO4J_DATABASE
            )
            
            return self._graph
            
        except Exception as e:
            logger.error(f"Error connecting to Neo4j: {str(e)}")
            return None
    
    def get_qa_chain(self) -> GraphCypherQAChain:
        """
        Get the QA chain for the graph with custom prompts for handling abbreviations
        
        Returns:
            GraphCypherQAChain instance with enhanced prompts
        """
        if self._qa_chain:
            return self._qa_chain
            
        # Get Neo4j graph connection
        graph = self.get_neo4j_graph()
        if not graph:
            raise ValueError("Neo4j connection not available")
            
        # Create LLM
        llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_2_0_FLASH)
        
        
        # Create custom prompts
        cypher_prompt = PromptTemplate(
            input_variables=["schema", "question"],
            template=CYPHER_GENERATION_TEMPLATE
        )
        
        qa_prompt = PromptTemplate(
            input_variables=["context", "question"],
            template=CYPHER_QA_TEMPLATE
        )
        
        # Create the chain with custom prompts
        self._qa_chain = GraphCypherQAChain.from_llm(
            llm=llm,
            graph=graph,
            cypher_prompt=cypher_prompt,
            qa_prompt=qa_prompt,
            verbose=True,
            return_intermediate_steps=True,
            allow_dangerous_requests=True  # Required for database access
        )
        
        return self._qa_chain
    
    def query_knowledge_graph(self, question: str) -> Dict[str, Any]:
        """
        Query the knowledge graph using LangChain's GraphCypherQAChain
        
        Args:
            question: The question to ask
            
        Returns:
            Dict containing the answer and intermediate steps (with serializable objects)
        """
        try:
            logger.info(f"Querying knowledge graph: {question}")
            
            # Get the QA chain
            chain = self.get_qa_chain()
            
            # Query the knowledge graph
            result = chain({"query": question})
            
            # Log the result
            logger.info(f"Generated Cypher query: {result.get('intermediate_steps', [{}])[0].get('query', 'No query found')}")
            
            # Check if the query returned empty results and the query contains relationship direction issues
            cypher_query = result.get('intermediate_steps', [{}])[0].get('query', '')
            
            # If we get no results and the query seems to have relationship direction issues, try alternative approach
            if (not result.get('result') or result.get('result') == 'No answer found') and 'INVOLVES' in cypher_query:
                logger.warning("Query returned no results, checking for relationship direction issues...")
                
                # Try with corrected relationship direction if the original query had wrong direction
                if '(team:Team)-[:INVOLVES]->(program:Program)' in cypher_query:
                    logger.info("Detected incorrect relationship direction, attempting fallback...")
                    corrected_query = cypher_query.replace(
                        '(team:Team)-[:INVOLVES]->(program:Program)',
                        '(program:Program)-[:INVOLVES]->(team:Team)'
                    )
                    
                    try:
                        # Execute the corrected query directly
                        graph = self.get_neo4j_graph()
                        if graph:
                            direct_result = graph.query(corrected_query)
                            if direct_result:
                                logger.info(f"Fallback query successful with corrected direction: {corrected_query}")
                                # Create a successful result with the corrected data
                                return {
                                    "status": "success",
                                    "question": question,
                                    "answer": f"Found match information: {direct_result}",
                                    "cypher_query": corrected_query,
                                    "context": direct_result,
                                    "raw_result": {"result": str(direct_result), "corrected": True}
                                }
                    except Exception as fallback_error:
                        logger.error(f"Fallback query also failed: {str(fallback_error)}")
            
            # Serialize the result to handle Neo4j objects
            serialized_result = serialize_neo4j_objects(result)
            
            # Return a structured result with serialized objects
            return {
                "status": "success",
                "question": question,
                "answer": serialized_result.get("result", "No answer found"),
                "cypher_query": serialized_result.get("intermediate_steps", [{}])[0].get("query", ""),
                "context": serialized_result.get("intermediate_steps", [{}])[0].get("context", []),
                "raw_result": serialized_result
            }
            
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {str(e)}")
            return {
                "status": "error",
                "question": question,
                "answer": f"An error occurred while querying the knowledge graph: {str(e)}",
                "cypher_query": "",
                "context": [],
                "raw_result": {}
            }
    
    def query_with_fallback_directions(self, question: str) -> Dict[str, Any]:
        """
        Query with automatic fallback for relationship direction issues
        
        Args:
            question: The question to ask
            
        Returns:
            Dict containing the answer with fallback handling
        """
        # First try the normal query
        result = self.query_knowledge_graph(question)
        
        # If it fails or returns no results, and it's a team-related query, try alternative patterns
        if (result.get("status") == "error" or not result.get("answer") or 
            result.get("answer") == "No answer found") and any(team in question.lower() 
            for team in ["match", "team", "csk", "mi", "rcb", "dc", "kkr", "pbks", "rr", "srh", "gt", "lsg"]):
            
            logger.info("Attempting fallback query with bidirectional relationship search...")
            
            try:
                graph = self.get_neo4j_graph()
                if not graph:
                    return result
                
                # Extract team name from question
                team_name = None
                for abbrev, full_name in self.team_abbreviations.items():
                    if abbrev.lower() in question.lower():
                        team_name = abbrev
                        break
                    elif full_name.lower() in question.lower():
                        team_name = full_name
                        break
                
                if team_name:
                    # Generate flexible condition
                    team_condition = self.generate_team_match_condition(team_name)
                    
                    # Try bidirectional query
                    fallback_query = f"""
                    MATCH (program:Program)-[:INVOLVES]->(team:Team)
                    WHERE {team_condition} AND program.start_time > datetime()
                    RETURN program.start_time AS NextMatchTime, program.name AS MatchName, team.name AS TeamName
                    ORDER BY program.start_time ASC 
                    LIMIT 1
                    """
                    
                    fallback_result = graph.query(fallback_query)
                    if fallback_result:
                        serialized_fallback = serialize_neo4j_objects(fallback_result)
                        return {
                            "status": "success",
                            "question": question,
                            "answer": f"Next match found: {serialized_fallback}",
                            "cypher_query": fallback_query,
                            "context": serialized_fallback,
                            "raw_result": {"result": serialized_fallback, "fallback": True}
                        }
                        
            except Exception as fallback_error:
                logger.error(f"Fallback query failed: {str(fallback_error)}")
        
        return result 