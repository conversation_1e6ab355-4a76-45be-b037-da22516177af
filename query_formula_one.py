#!/usr/bin/env python3
"""
Query Formula One program schedule from the Neo4j knowledge graph
"""

try:
    from epg_tv_guide.services.knowledge_graph_service import KnowledgeGraphService
    
    # Initialize the knowledge graph service
    print("Initializing knowledge graph service...")
    kg_service = KnowledgeGraphService()
    
    # Query for Formula One program airing information
    question = "When is the Formula One program airing?"
    print(f"Querying: {question}")
    
    result = kg_service.query_knowledge_graph(question)
    
    print("\n=== Formula One Program Schedule ===")
    print(f"Question: {question}")
    print(f"Status: {result.get('status', 'unknown')}")
    print(f"Answer: {result.get('answer', 'No answer found')}")
    print(f"Generated Cypher Query: {result.get('cypher_query', 'No query generated')}")
    
    if result.get('context'):
        print(f"Context: {result.get('context', [])}")
        
    # Check if the query uses the correct BROADCASTS relationship
    cypher_query = result.get('cypher_query', '').lower()
    if 'broadcasts' in cypher_query:
        print("✅ Query uses correct BROADCASTS relationship")
    elif 'airs_on' in cypher_query:
        print("⚠️  Query uses incorrect AIRS_ON relationship")
    else:
        print("ℹ️  Relationship type unclear from query")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
