# Relationship Checking Sub-Agent Documentation

## Overview

The Relationship Checking Sub-Agent is a specialized component designed to validate and correct relationship usage in Neo4j Cypher queries. It prevents query failures by ensuring only existing relationships are used and provides intelligent recommendations for correct alternatives.

## Problem Solved

**Before:** Queries failed when using non-existent relationships like `<PERSON>RS_ON`
```cypher
MATCH (p:Program)-[:AIRS_ON]->(c:Channel)  -- FAILS: AIRS_ON doesn't exist
```

**After:** Sub-agent detects and corrects to existing relationships
```cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program)  -- WORKS: BROADCASTS exists
```

## Core Function: `check_relationship_exists()`

### Function Signature
```python
def check_relationship_exists(
    relationship_name: str, 
    source_node: str = None, 
    target_node: str = None
) -> Dict[str, Any]
```

### Parameters
- **`relationship_name`**: The relationship type to check (e.g., 'AIRS_ON', 'BROADCASTS')
- **`source_node`**: Optional source node label (e.g., 'Program')
- **`target_node`**: Optional target node label (e.g., 'Channel')

### Return Value
```python
{
    "status": "success",
    "relationship_name": "AIRS_ON",
    "exists_in_database": False,
    "all_available_relationships": ["BROADCASTS", "INVOLVES", "COMPETES_IN", ...],
    "node_specific_check": {
        "exists_between_nodes": False,
        "count": 0
    },
    "alternative_relationships": [
        {"relationship": "BROADCASTS", "count": 1250}
    ],
    "recommendation": "BROADCASTS"
}
```

## Usage Examples

### 1. Basic Relationship Check
```python
from epg_tv_guide import check_relationship_exists

# Check if AIRS_ON exists
result = check_relationship_exists("AIRS_ON")
print(result['exists_in_database'])  # False
print(result['recommendation'])      # "BROADCASTS"
```

### 2. Node-Specific Relationship Check
```python
# Check if AIRS_ON exists between Program and Channel nodes
result = check_relationship_exists("AIRS_ON", "Program", "Channel")
print(result['node_specific_check']['exists_between_nodes'])  # False
print(result['alternative_relationships'])  # [{"relationship": "BROADCASTS", "count": 1250}]
```

### 3. Verify Correct Relationship
```python
# Check if BROADCASTS exists
result = check_relationship_exists("BROADCASTS", "Program", "Channel")
print(result['exists_in_database'])  # True
print(result['node_specific_check']['count'])  # 1250
```

## Integration with Query Validation

The sub-agent is automatically integrated into the query validation workflow:

### Enhanced `validate_and_correct_query()` Function

```python
def validate_and_correct_query(question: str) -> Dict[str, Any]:
    # 1. Extract mentioned relationships from question
    if "AIRS_ON" in question:
        mentioned_relationship = "AIRS_ON"
    
    # 2. Use sub-agent to check relationship
    rel_check = check_relationship_exists(mentioned_relationship, "Program", "Channel")
    
    # 3. If relationship doesn't exist, use recommendation
    if not rel_check.get('exists_in_database', False):
        recommended_rel = rel_check.get('recommendation')
        # Generate corrected query with proper relationship
```

## Agent Integration

The sub-agent is available as a tool in the main agent:

```python
super_sport_agent = Agent(
    name="super_sport_agent",
    tools=[query_graph, inspect_schema, check_relationship_exists]
)
```

### Agent Instructions Include:
- Automatic relationship verification
- Intelligent error correction
- Schema-aware query generation

## Workflow Example

### Input Question with Wrong Relationship
```
"When is Formula One airing? IMPORTANT: Use relationship 'AIRS_ON'"
```

### Sub-Agent Processing
1. **Detect**: Question mentions 'AIRS_ON' relationship
2. **Check**: `check_relationship_exists("AIRS_ON", "Program", "Channel")`
3. **Result**: `exists_in_database: False, recommendation: "BROADCASTS"`
4. **Correct**: Replace AIRS_ON with BROADCASTS in query generation
5. **Generate**: Working Cypher with correct relationship

### Final Output
```cypher
MATCH (c:Channel)-[:BROADCASTS]->(p:Program) 
WHERE LOWER(p.name) CONTAINS LOWER('formula one')
RETURN c.name, p.name, p.start_time
```

## Key Features

### 🔍 **Comprehensive Checking**
- Verifies relationship existence in database
- Checks specific node-to-node relationships
- Provides usage statistics

### 🎯 **Intelligent Recommendations**
- Suggests most commonly used alternatives
- Ranks alternatives by usage frequency
- Provides context-aware suggestions

### 🔧 **Automatic Correction**
- Seamlessly integrates with query validation
- Prevents query failures before they occur
- Maintains query intent while fixing relationships

### 📊 **Detailed Reporting**
- Lists all available relationships
- Provides relationship usage counts
- Reports specific error details

## Error Handling

### Database Connection Issues
```python
{
    "status": "error",
    "message": "Could not connect to Neo4j database",
    "relationship_name": "AIRS_ON"
}
```

### Invalid Relationship Queries
```python
{
    "status": "success",
    "exists_in_database": False,
    "node_specific_check": {
        "exists_between_nodes": False,
        "error": "Invalid node labels"
    }
}
```

## Testing

### Run Comprehensive Tests
```bash
python test_relationship_subagent.py
```

### Manual Testing
```python
from epg_tv_guide import check_relationship_exists

# Test non-existent relationship
result = check_relationship_exists("AIRS_ON", "Program", "Channel")
assert result['exists_in_database'] == False
assert result['recommendation'] == "BROADCASTS"

# Test existing relationship
result = check_relationship_exists("BROADCASTS", "Program", "Channel")
assert result['exists_in_database'] == True
```

## Benefits

### 🚀 **Improved Reliability**
- Eliminates query failures due to wrong relationships
- Provides consistent, working Cypher queries
- Reduces debugging time

### 🧠 **Intelligent Assistance**
- Learns from database schema automatically
- Provides context-aware recommendations
- Adapts to schema changes

### 🔄 **Seamless Integration**
- Works transparently with existing queries
- No changes needed to user questions
- Maintains backward compatibility

### 📈 **Enhanced User Experience**
- Users get correct results instead of errors
- No need to know exact relationship names
- Automatic error correction and suggestions

## Future Enhancements

- **Relationship Pattern Learning**: Learn common relationship patterns
- **Multi-hop Relationship Checking**: Validate complex relationship paths
- **Performance Optimization**: Cache relationship checks for better performance
- **Advanced Recommendations**: Context-aware relationship suggestions based on query intent
