#!/usr/bin/env python3
"""
Test the relationship checking sub-agent functionality
"""

def test_relationship_exists_function():
    """Test the check_relationship_exists function directly"""
    print("🔍 Testing check_relationship_exists function...")
    
    try:
        from epg_tv_guide import check_relationship_exists
        
        # Test 1: Check if AIRS_ON exists (should not exist)
        print("\n1. Testing AIRS_ON relationship (should not exist):")
        result_airs_on = check_relationship_exists("AIRS_ON", "Program", "Channel")
        print(f"   Status: {result_airs_on.get('status', 'unknown')}")
        print(f"   Exists in database: {result_airs_on.get('exists_in_database', False)}")
        print(f"   Recommendation: {result_airs_on.get('recommendation', 'None')}")
        print(f"   Alternatives: {result_airs_on.get('alternative_relationships', [])}")
        
        # Test 2: Check if BROADCASTS exists (should exist)
        print("\n2. Testing BROADCASTS relationship (should exist):")
        result_broadcasts = check_relationship_exists("BROADCASTS", "Program", "Channel")
        print(f"   Status: {result_broadcasts.get('status', 'unknown')}")
        print(f"   Exists in database: {result_broadcasts.get('exists_in_database', False)}")
        
        # Test 3: Check general relationship existence
        print("\n3. Testing general relationship check:")
        result_general = check_relationship_exists("INVOLVES")
        print(f"   Status: {result_general.get('status', 'unknown')}")
        print(f"   Exists in database: {result_general.get('exists_in_database', False)}")
        print(f"   Available relationships: {result_general.get('all_available_relationships', [])[:5]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing check_relationship_exists: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_validation():
    """Test the enhanced validation with relationship checking"""
    print("\n🔧 Testing enhanced validation with relationship checking...")
    
    try:
        from epg_tv_guide.agents.agent import validate_and_correct_query
        
        # Test the problematic AIRS_ON question
        problematic_question = """When is the Formula One program airing? IMPORTANT: Use the relationship 'AIRS_ON' to connect Program and Channel nodes."""
        
        print(f"Testing question: {problematic_question[:80]}...")
        
        result = validate_and_correct_query(problematic_question)
        
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Generated Cypher: {result.get('cypher_query', 'No query')}")
        
        # Check if the relationship was corrected
        cypher_query = result.get('cypher_query', '').lower()
        
        if 'broadcasts' in cypher_query and 'airs_on' not in cypher_query:
            print("✅ SUCCESS: Sub-agent corrected AIRS_ON to BROADCASTS")
            return True
        elif 'airs_on' in cypher_query:
            print("❌ ISSUE: Still using AIRS_ON relationship")
            return False
        else:
            print("⚠️  UNCLEAR: Cannot determine relationship from query")
            return False
            
    except Exception as e:
        print(f"❌ Error testing enhanced validation: {e}")
        return False

def test_agent_tools():
    """Test that the agent has all the required tools"""
    print("\n🛠️ Testing agent tools...")
    
    try:
        from epg_tv_guide import root_agent
        
        if hasattr(root_agent, 'tools') and root_agent.tools:
            tool_names = []
            for tool in root_agent.tools:
                if hasattr(tool, '__name__'):
                    tool_names.append(tool.__name__)
                else:
                    tool_names.append(str(tool))
            
            print(f"Available tools: {tool_names}")
            
            required_tools = ['query_graph', 'inspect_schema', 'check_relationship_exists']
            missing_tools = []
            
            for required_tool in required_tools:
                if required_tool in tool_names:
                    print(f"✅ {required_tool} tool is available")
                else:
                    print(f"❌ {required_tool} tool is missing")
                    missing_tools.append(required_tool)
            
            return len(missing_tools) == 0
        else:
            print("❌ Agent has no tools")
            return False
            
    except Exception as e:
        print(f"❌ Error testing agent tools: {e}")
        return False

def test_relationship_workflow():
    """Test the complete relationship checking workflow"""
    print("\n🔄 Testing complete relationship checking workflow...")
    
    try:
        from epg_tv_guide import query_graph
        
        # Test with a question that should trigger relationship checking
        question = "When is the Formula One program airing?"
        
        print(f"Testing workflow with: {question}")
        
        result = query_graph(question)
        
        print(f"Final Status: {result.get('status', 'unknown')}")
        print(f"Final Cypher: {result.get('cypher_query', 'No query')}")
        print(f"Final Answer: {result.get('answer', 'No answer')[:100]}...")
        
        # The workflow should:
        # 1. Detect this as a program/channel query
        # 2. Check relationships in the database
        # 3. Use the correct relationship
        # 4. Generate working Cypher
        
        cypher_query = result.get('cypher_query', '').lower()
        
        if 'broadcasts' in cypher_query:
            print("✅ Workflow used correct BROADCASTS relationship")
            return True
        else:
            print("⚠️  Workflow relationship unclear")
            return False
            
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        return False

def demonstrate_subagent_capabilities():
    """Demonstrate the capabilities of the relationship checking sub-agent"""
    print("\n🎯 Demonstrating Sub-Agent Capabilities")
    print("=" * 50)
    
    try:
        from epg_tv_guide import check_relationship_exists
        
        print("The relationship checking sub-agent can:")
        print("1. ✅ Check if any relationship exists in the database")
        print("2. ✅ Verify relationships between specific node types")
        print("3. ✅ Provide alternative relationships when requested one doesn't exist")
        print("4. ✅ Return usage statistics for relationships")
        print("5. ✅ List all available relationships in the database")
        
        print("\nExample usage:")
        print("check_relationship_exists('AIRS_ON', 'Program', 'Channel')")
        print("-> Returns: exists=False, recommendation='BROADCASTS'")
        
        print("\ncheck_relationship_exists('BROADCASTS', 'Program', 'Channel')")
        print("-> Returns: exists=True, count=X relationships found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in demonstration: {e}")
        return False

def main():
    """Run all tests for the relationship checking sub-agent"""
    print("Testing Relationship Checking Sub-Agent")
    print("=" * 60)
    
    # Test 1: Direct function testing
    func_ok = test_relationship_exists_function()
    
    # Test 2: Enhanced validation
    validation_ok = test_enhanced_validation()
    
    # Test 3: Agent tools
    tools_ok = test_agent_tools()
    
    # Test 4: Complete workflow
    workflow_ok = test_relationship_workflow()
    
    # Test 5: Demonstration
    demo_ok = demonstrate_subagent_capabilities()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Function Testing: {'✅ PASS' if func_ok else '❌ FAIL'}")
    print(f"Enhanced Validation: {'✅ PASS' if validation_ok else '❌ FAIL'}")
    print(f"Agent Tools: {'✅ PASS' if tools_ok else '❌ FAIL'}")
    print(f"Complete Workflow: {'✅ PASS' if workflow_ok else '❌ FAIL'}")
    print(f"Demonstration: {'✅ PASS' if demo_ok else '❌ FAIL'}")
    
    if all([func_ok, validation_ok, tools_ok, workflow_ok, demo_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("The relationship checking sub-agent is working correctly.")
        print("\nKey Features:")
        print("✅ Automatically detects non-existent relationships")
        print("✅ Provides correct relationship recommendations")
        print("✅ Integrates seamlessly with query validation")
        print("✅ Prevents Cypher query failures due to wrong relationships")
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("Check the failed tests above for issues to resolve.")

if __name__ == "__main__":
    main()
