#!/usr/bin/env python3
"""
Direct test of the Formula One query functionality
"""

def test_direct_query_graph():
    """Test the query_graph function directly"""
    print("🔍 Testing query_graph function directly...")
    try:
        from epg_tv_guide.agents.agent import query_graph
        
        question = "When is the Formula One program airing?"
        print(f"Question: {question}")
        
        result = query_graph(question)
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Answer: {result.get('answer', 'No answer')}")
        print(f"Cypher Query: {result.get('cypher_query', 'No query')}")
        
        if result.get('status') == 'success':
            print("✅ Query executed successfully")
            return True
        else:
            print(f"❌ Query failed: {result.get('answer', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing query_graph: {e}")
        return False

def test_agent_tools():
    """Test that the agent has the correct tools"""
    print("\n🔧 Testing agent tools...")
    try:
        from epg_tv_guide import root_agent
        
        if hasattr(root_agent, 'tools') and root_agent.tools:
            tool_names = []
            for tool in root_agent.tools:
                if hasattr(tool, '__name__'):
                    tool_names.append(tool.__name__)
                else:
                    tool_names.append(str(tool))
            
            print(f"Available tools: {tool_names}")
            
            if 'query_graph' in tool_names:
                print("✅ query_graph tool is available")
            else:
                print("❌ query_graph tool is missing")
                
            if 'inspect_schema' in tool_names:
                print("✅ inspect_schema tool is available")
            else:
                print("❌ inspect_schema tool is missing")
                
            return 'query_graph' in tool_names
        else:
            print("❌ Agent has no tools")
            return False
            
    except Exception as e:
        print(f"❌ Error testing agent tools: {e}")
        return False

def test_neo4j_connection():
    """Test Neo4j connection"""
    print("\n🔗 Testing Neo4j connection...")
    try:
        from epg_tv_guide.services.knowledge_graph_service import KnowledgeGraphService
        
        service = KnowledgeGraphService()
        graph = service.get_neo4j_graph()
        
        if graph:
            print("✅ Neo4j connection established")
            return True
        else:
            print("❌ Neo4j connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Neo4j connection error: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Formula One Query Functionality")
    print("=" * 50)
    
    # Test Neo4j connection first
    neo4j_ok = test_neo4j_connection()
    
    # Test agent tools
    tools_ok = test_agent_tools()
    
    # Test direct query if connection is working
    if neo4j_ok:
        query_ok = test_direct_query_graph()
    else:
        print("\n⚠️  Skipping query test due to Neo4j connection issues")
        query_ok = False
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Neo4j Connection: {'✅ OK' if neo4j_ok else '❌ FAILED'}")
    print(f"Agent Tools: {'✅ OK' if tools_ok else '❌ FAILED'}")
    print(f"Query Function: {'✅ OK' if query_ok else '❌ FAILED'}")
    
    if neo4j_ok and tools_ok and query_ok:
        print("\n🎉 All tests passed! The Formula One query should work.")
        print("\nTo use the agent:")
        print("1. Import: from epg_tv_guide import root_agent")
        print("2. Ask: 'When is the Formula One program airing?'")
        print("3. The agent should call query_graph() automatically")
    else:
        print("\n💥 Some tests failed. Issues to fix:")
        if not neo4j_ok:
            print("- Check Neo4j database connection and credentials")
        if not tools_ok:
            print("- Verify agent tools are properly configured")
        if not query_ok:
            print("- Debug query_graph function implementation")

if __name__ == "__main__":
    main()
