"""
SuperSport Agent Package

This package provides an intelligent agent for retrieving SuperSport information
directly from a Neo4j knowledge graph using LangChain.
"""

# Load environment variables from .env file if present
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not installed. Environment variables must be set manually.")

# Import version info
__version__ = "1.0.0"

# Import agents (sub-agent only implementation)
from .agents.agent import (
    super_sport_agent,
    agent,
    root_agent,
    relationship_validator_agent,
    query_executor_agent,
    execute_final_query
)

# Import services
from .services.knowledge_graph_service import KnowledgeGraphService

# Import utilities
from .utils import get_logger

# Exportable package elements
__all__ = [
    # Agents (Sub-Agent Only Implementation)
    "super_sport_agent",
    "agent",
    "root_agent",
    "relationship_validator_agent",
    "query_executor_agent",

    # Services
    "KnowledgeGraphService",

    # Utilities
    "get_logger"
]