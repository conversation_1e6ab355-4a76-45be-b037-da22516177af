#!/usr/bin/env python3
"""
Demonstration of how to properly use the enhanced agent for Formula One queries
"""

def demo_formula_one_query():
    """Demonstrate the Formula One query with the enhanced agent"""
    print("🏎️ Formula One Query Demonstration")
    print("=" * 50)
    
    try:
        # Import the enhanced agent
        from epg_tv_guide import root_agent, query_graph
        
        print("✅ Successfully imported enhanced agent")
        print(f"Agent name: {root_agent.name}")
        print(f"Agent tools: {[tool.__name__ for tool in root_agent.tools if hasattr(tool, '__name__')]}")
        
        # Test the direct query_graph function first
        print("\n🔍 Testing direct query_graph function...")
        question = "When is the Formula One program airing?"
        
        result = query_graph(question)
        
        print(f"Question: {question}")
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Generated Cypher: {result.get('cypher_query', 'No query generated')}")
        print(f"Answer: {result.get('answer', 'No answer provided')}")
        
        # Check if the query uses the correct BROADCASTS relationship
        cypher_query = result.get('cypher_query', '').lower()
        if 'broadcasts' in cypher_query:
            print("✅ Query uses correct BROADCASTS relationship")
        elif 'airs_on' in cypher_query:
            print("⚠️  Query still uses incorrect AIRS_ON relationship")
        else:
            print("ℹ️  Relationship type unclear from query")
        
        return result
        
    except Exception as e:
        print(f"❌ Error in demonstration: {e}")
        import traceback
        traceback.print_exc()
        return None

def demo_schema_inspection():
    """Demonstrate the schema inspection functionality"""
    print("\n🔍 Schema Inspection Demonstration")
    print("=" * 50)
    
    try:
        from epg_tv_guide import inspect_schema
        
        schema_info = inspect_schema()
        
        if schema_info.get('status') == 'success':
            print("✅ Schema inspection successful")
            print(f"Available relationships: {schema_info.get('relationships', [])}")
            print(f"Node labels: {schema_info.get('node_labels', [])}")
            
            # Show Program-Channel relationships specifically
            prog_chan_rels = schema_info.get('program_channel_relationships', [])
            if prog_chan_rels:
                print(f"Program-Channel relationships: {prog_chan_rels}")
            else:
                print("No Program-Channel relationships found")
        else:
            print(f"❌ Schema inspection failed: {schema_info.get('message', 'Unknown error')}")
            
        return schema_info
        
    except Exception as e:
        print(f"❌ Error in schema inspection: {e}")
        return None

def demo_agent_conversation():
    """Demonstrate how the agent should respond to Formula One questions"""
    print("\n🤖 Agent Conversation Demonstration")
    print("=" * 50)
    
    print("User: When is the Formula One program airing?")
    print("\nExpected Agent Behavior:")
    print("1. Agent should recognize this as a program schedule question")
    print("2. Agent should IMMEDIATELY call query_graph('When is the Formula One program airing?')")
    print("3. Agent should use the enhanced schema validation")
    print("4. Agent should generate Cypher with BROADCASTS relationship")
    print("5. Agent should return the schedule information from Neo4j")
    print("\nAgent should NOT say: 'I cannot access TV schedules'")
    print("Agent SHOULD say: Information from the knowledge graph about Formula One scheduling")

def main():
    """Run the complete demonstration"""
    print("Enhanced EPG Chat Bot - Formula One Query Solution")
    print("=" * 60)
    
    # Demo 1: Direct query function
    query_result = demo_formula_one_query()
    
    # Demo 2: Schema inspection
    schema_result = demo_schema_inspection()
    
    # Demo 3: Expected agent behavior
    demo_agent_conversation()
    
    print("\n" + "=" * 60)
    print("SOLUTION SUMMARY:")
    print("✅ Enhanced agent with schema inspection sub-agent")
    print("✅ Automatic query validation and relationship correction")
    print("✅ Explicit instructions to always use knowledge graph")
    print("✅ Correct BROADCASTS relationship usage")
    
    if query_result and query_result.get('status') == 'success':
        print("✅ Formula One query is working correctly")
    else:
        print("⚠️  Formula One query needs troubleshooting")
        print("\nTroubleshooting steps:")
        print("1. Verify Neo4j database is running and accessible")
        print("2. Check Neo4j credentials in settings.py")
        print("3. Ensure Formula One program data exists in the database")
        print("4. Test with: python formula_one_demo.py")

if __name__ == "__main__":
    main()
