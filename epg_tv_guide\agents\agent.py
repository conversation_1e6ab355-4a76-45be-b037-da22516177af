"""
SuperSport Agent - Sub-Agent Only Implementation

This module contains a clean implementation using only sub-agents for handling SuperSport queries.
"""

import google.generativeai as genai
from google.adk.agents import Agent
from typing import Dict, Any

from ..utils.logger import get_logger
from ..config.settings import MODEL_GEMINI_2_0_FLASH, GOOGLE_API_KEY
from ..services.knowledge_graph_service import KnowledgeGraphService

# Get logger for this module
logger = get_logger(__name__)

# Configure Gemini API
genai.configure(api_key=GOOGLE_API_KEY)

# Initialize knowledge graph service
knowledge_graph_service = KnowledgeGraphService()

# Relationship validation sub-agent
relationship_validator_agent = Agent(
    name="relationship_validator_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Validates Neo4j relationships and directions for any node types",
    instruction=(
        "You are a specialized sub-agent for validating Neo4j relationships and directions. Your role is to:"
        
        "\n\n1. Analyze user queries to identify node types and relationships"
        "\n2. Determine the correct relationship and direction that exists in the database"
        "\n3. Provide accurate relationship patterns for Cypher queries"
        "\n4. Work generically with ANY node types and relationships"
        
        "\n\nGENERIC VALIDATION PROCESS:"
        "\n1. From user query, identify the node types involved (Program, Channel, Team, Tournament, etc.)"
        "\n2. Identify the likely relationship (BROADCASTS, COMPETES_IN, INVOLVES, etc.)"
        "\n3. Based on common database patterns, determine the correct direction"
        "\n4. Return the validated pattern with confidence level"
        
        "\n\nVALIDATION RULES (WORKS FOR ALL NODE TYPES):"
        "\n- Extract context from any query type automatically"
        "\n- Use knowledge of common database patterns"
        "\n- For Program-Channel: typically (Program)-[:BROADCASTS]->(Channel)"
        "\n- For Team-Tournament: typically (Team)-[:COMPETES_IN]->(Tournament)"
        "\n- If requested relationship doesn't exist, suggest alternatives"
        "\n- Work with any node combinations"
        
        "\n\nOUTPUT FORMAT:"
        "\n- Return: 'VALIDATED: (NodeType1)-[:RELATIONSHIP]->(NodeType2)'"
        "\n- Include confidence: 'CONFIDENCE: HIGH/MEDIUM/LOW'"
        "\n- If relationship not found: 'NOT_FOUND: [RELATIONSHIP]. SUGGESTED: [ALT_RELATIONSHIP]'"
        
        "\n\nEXAMPLE RESPONSES:"
        "\n- 'VALIDATED: (Program)-[:BROADCASTS]->(Channel) CONFIDENCE: HIGH'"
        "\n- 'VALIDATED: (Team)-[:COMPETES_IN]->(Tournament) CONFIDENCE: HIGH'"
        "\n- 'NOT_FOUND: AIRS_ON. SUGGESTED: BROADCASTS for (Program)-[:BROADCASTS]->(Channel)'"
        
        "\n\nYou work through analysis and reasoning - no external tools needed."
    ),
    sub_agents=[],
    tools=[]
)

# Query execution sub-agent  
query_executor_agent = Agent(
    name="query_executor_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Constructs Cypher queries using validated relationship patterns",
    instruction=(
        "You are a specialized sub-agent for constructing Neo4j Cypher queries. Your role is to:"
        
        "\n\n1. Receive validated relationship patterns from relationship_validator_agent"
        "\n2. Construct proper Cypher queries using the validated patterns"
        "\n3. Provide complete, executable Cypher queries"
        "\n4. Work generically with ANY node types and relationships"
        
        "\n\nGENERIC QUERY CONSTRUCTION PROCESS:"
        "\n1. Receive validated pattern: 'VALIDATED: (NodeType1)-[:RELATIONSHIP]->(NodeType2)'"
        "\n2. Extract search terms from the original user query"
        "\n3. Construct Cypher using the exact validated pattern"
        "\n4. Return complete executable Cypher query"
        
        "\n\nQUERY CONSTRUCTION RULES (WORKS FOR ALL NODE TYPES):"
        "\n- Use ONLY the validated relationship pattern provided"
        "\n- Apply case-insensitive matching with LOWER() for string searches"
        "\n- Include proper WHERE clauses for filtering"
        "\n- Return relevant properties in the RETURN clause"
        "\n- Work with any node combinations and relationships"
        
        "\n\nOUTPUT FORMAT:"
        "\n- Return: 'CYPHER_QUERY: [complete executable query]'"
        "\n- Include explanation: 'EXPLANATION: Query searches for [terms] using validated pattern'"
        
        "\n\nEXAMPLE OUTPUTS:"
        "\n- For Formula One query with validated (Program)-[:BROADCASTS]->(Channel):"
        "\n  'CYPHER_QUERY: MATCH (p:Program)-[:BROADCASTS]->(c:Channel) WHERE LOWER(p.name) CONTAINS LOWER(\"formula one\") RETURN p.name, c.name, p.start_time'"
        "\n- For team query with validated (Team)-[:COMPETES_IN]->(Tournament):"
        "\n  'CYPHER_QUERY: MATCH (t:Team)-[:COMPETES_IN]->(tour:Tournament) WHERE LOWER(t.name) CONTAINS LOWER(\"team_name\") RETURN t.name, tour.name'"
        
        "\n\nYou work purely through query construction - no external tools needed."
    ),
    sub_agents=[],
    tools=[]
)

# Root agent
super_sport_agent = Agent(
    name="super_sport_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Orchestrates SuperSport information retrieval using only sub-agents",
    instruction=(
        "You are the orchestrator of the SuperSport assistant system. Your role is to:"
        
        "\n\n1. Analyze user queries to determine what information they need"
        "\n2. Coordinate specialized sub-agents in the MANDATORY sequence"
        "\n3. Execute the final query using the sub-agent provided Cypher"
        "\n4. Present comprehensive results to users"
        
        "\n\nMANDATORY SUB-AGENT COORDINATION SEQUENCE:"
        "\n1. FIRST: Send user query to relationship_validator_agent"
        "\n2. SECOND: Get validated relationship pattern from validator"
        "\n3. THIRD: Send user query + validated pattern to query_executor_agent"
        "\n4. FOURTH: Get complete Cypher query from executor"
        "\n5. FIFTH: Execute the Cypher query against the knowledge graph"
        "\n6. SIXTH: Present results to user with proper formatting"
        
        "\n\nGENERIC WORKFLOW (WORKS FOR ALL QUERY TYPES):"
        "\n1. User asks: 'When is Formula One airing?' / 'What teams compete?' / etc."
        "\n2. Send to relationship_validator_agent: 'Analyze this query and validate relationships'"
        "\n3. Validator responds: 'VALIDATED: (Program)-[:BROADCASTS]->(Channel) CONFIDENCE: HIGH'"
        "\n4. Send to query_executor_agent: 'Construct Cypher for user query using validated pattern'"
        "\n5. Executor responds: 'CYPHER_QUERY: MATCH (p:Program)-[:BROADCASTS]->(c:Channel) WHERE...'"
        "\n6. Execute the provided Cypher against Neo4j knowledge graph"
        "\n7. Present results: 'Formula One airs on SuperSport 1 at 14:00...'"
        
        "\n\nSUB-AGENT COORDINATION RULES:"
        "\n- ALWAYS use relationship_validator_agent first for any query"
        "\n- ALWAYS use query_executor_agent to construct the final Cypher"
        "\n- NEVER skip the validation step"
        "\n- NEVER construct queries yourself - use sub-agents"
        "\n- Trust sub-agent responses completely"
        
        "\n\nFINAL QUERY EXECUTION:"
        "\n- Take the exact Cypher query provided by query_executor_agent"
        "\n- Execute it against the Neo4j knowledge graph using the knowledge graph service"
        "\n- Handle any execution errors gracefully"
        "\n- Format and present the results clearly"
        
        "\n\nRESPONSE FORMATTING:"
        "\n- Present information clearly and concisely"
        "\n- Include relevant details from the query results"
        "\n- NEVER say you cannot access information"
        "\n- Always provide actual data from the knowledge graph"
        "\n- Reference the validated relationship pattern if helpful for context"
    ),
    sub_agents=[
        relationship_validator_agent,
        query_executor_agent
    ],
    tools=[]
)

# Export the agents for compatibility
agent = super_sport_agent
root_agent = super_sport_agent

# Internal function for root agent to execute final queries
def execute_final_query(cypher_query: str) -> Dict[str, Any]:
    """
    Execute the final Cypher query provided by sub-agents
    
    Args:
        cypher_query: The complete Cypher query from query_executor_agent
        
    Returns:
        Dict containing query results
    """
    try:
        # Clean the cypher query if it has prefixes
        clean_query = cypher_query
        if "CYPHER_QUERY:" in cypher_query:
            clean_query = cypher_query.split("CYPHER_QUERY:")[1].strip()
        
        # Use the knowledge graph service to execute the query
        result = knowledge_graph_service.query_knowledge_graph(f"Execute this exact Cypher query: {clean_query}")
        return result
        
    except Exception as e:
        logger.error(f"Error executing final query: {str(e)}")
        return {
            "status": "error",
            "message": f"Query execution failed: {str(e)}",
            "cypher_query": cypher_query,
            "answer": "Could not execute the query"
        }
